import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:xr_helper/xr_helper.dart';

class TicketRepository with BaseRepository {
  final BaseApiServices networkApiService;

  TicketRepository({
    required this.networkApiService,
  });

  // * Get Ticket Details
  Future<TicketModel> getTicketDetails({
    required int? ticketId,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.ticketDetails(ticketId);

        final response = await networkApiService.getResponse(
          url,
        );

        if (response == null ||
            response['dt'] == null ||
            response['dt'].isEmpty) {
          return TicketModel.empty();
        }

        final ticket = TicketModel.fromJson(response['dt'][0]);

        return ticket;
      },
    );
  }

  // * Get Ticket Reports by (Start Date, End Date)
  Future<List<TicketModel>> getTicketReports({
    required String? startDate,
    required String? endDate,
  }) async {
    return baseFunction(
      () async {
        final url = ApiEndpoints.ticketReports(startDate, endDate);

        final response = await networkApiService.getResponse(
          url,
        );

        if (response == null ||
            response['dt'] == null ||
            response['dt'].isEmpty) {
          return <TicketModel>[];
        }

        final tickets = List<TicketModel>.from(
          response['dt']['helpdesk'].map(
            (ticket) => TicketModel.fromJson(ticket),
          ),
        );

        return tickets;
      },
    );
  }

  // * Add Reply
  Future<void> addReply({
    required ReplyModel reply,
    String? filePath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.addReply;

        final deviceToken = await NotificationService.getToken();

        final response = await networkApiService.postResponse(
          url,
          body: reply.toJson(deviceToken: deviceToken),
          filePaths: filePath != null && filePath.isNotEmpty ? [filePath] : [],
          fieldName: 'th_attach',
        );

        if (response == null) {
          throw Exception('Failed to add reply');
        }
      },
    );
  }

  // * Add Ticket
  Future<void> addTicket({
    required TicketModel ticket,
    String? filePath,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.addTicket;

        final deviceToken = await NotificationService.getToken();

        final response = await networkApiService.postResponse(
          url,
          body: ticket.toJson(deviceToken: deviceToken),
          filePaths: filePath != null && filePath.isNotEmpty ? [filePath] : [],
          fieldName: 'ticketAttach',
        );

        if (response == null) {
          throw Exception('Failed to add ticket');
        }
      },
    );
  }
}
