import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/services/media/ui/media_picker.widget.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/lists/base_list.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:xr_helper/xr_helper.dart';

class RepliesSection extends StatelessWidget {
  final TicketModel ticketDetails;

  const RepliesSection({super.key, required this.ticketDetails});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: ticketDetails.replies ?? ValueNotifier(<ReplyModel>[]),
        builder: (context, replies, child) {
          return BaseList(
              emptyText: context.tr.noRepliesFound,
              physics: const NeverScrollableScrollPhysics(),
              separatorGap: Divider(
                color: ColorManager.darkGrey.withOpacity(.4),
              ),
              data: replies,
              itemBuilder: (context, index) {
                final reply = replies[index];

                return Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: AppSpaces.padding4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 20,
                            backgroundColor: ColorManager.primaryColor,
                            child: Text(
                              reply.person?.name?.substring(0, 1) ?? '',
                              style: AppTextStyles.whiteTitle
                                  .copyWith(fontSize: 20),
                            ),
                          ),
                          AppGaps.gap8,
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                reply.person?.name ?? '',
                                style: AppTextStyles.labelLarge,
                              ),
                              AppGaps.gap2,
                              Text(
                                reply.createdAt.formatDateToStringWithTime,
                                style: AppTextStyles.greyHint,
                              ),
                            ],
                          ),
                        ],
                      ),
                      AppGaps.gap8,
                      Text(
                        reply.message,
                        style: AppTextStyles.subTitle,
                      ),
                      if (reply.attach.isNotEmpty) ...[
                        AppGaps.gap8,
                        ViewNetworkFile(fileUrl: reply.attach),
                      ],
                    ],
                  ),
                );
              });
        });
  }
}
