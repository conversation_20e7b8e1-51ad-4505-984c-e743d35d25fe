import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/ticket_controller.dart';
import '../repositories/ticket_repository.dart';

// * Ticket Repo Provider ========================================
final ticketRepoProvider = Provider<TicketRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return TicketRepository(networkApiService: networkApiService);
});

// * Ticket Change Notifier Provider ========================================
final ticketControllerNotifierProvider =
    ChangeNotifierProvider<TicketController>(
  (ref) {
    final ticketRepo = ref.watch(ticketRepoProvider);

    return TicketController(
      ticketRepo: ticketRepo,
    );
  },
);

// * Ticket Provider ========================================
final ticketControllerProvider = Provider<TicketController>(
  (ref) {
    final ticketRepo = ref.watch(ticketRepoProvider);

    return TicketController(
      ticketRepo: ticketRepo,
    );
  },
);

// * Get Ticket Future Provider ========================================
final getTicketDetailsFutureProvider = FutureProvider.family(
  (ref, int? ticketId) {
    final ticketController = ref.watch(ticketControllerProvider);

    return ticketController.getTicketDetails(ticketId: ticketId);
  },
);

// * Get Ticket Reports Future Provider ========================================
final getTicketReportsFutureProvider = FutureProvider.family(
  (ref, (String? startDate, String? endDate) params) {
    final ticketController = ref.watch(ticketControllerProvider);

    final startDate = params.$1;
    final endDate = params.$2;

    return ticketController.getTicketReports(
      startDate: startDate,
      endDate: endDate,
    );
  },
);
