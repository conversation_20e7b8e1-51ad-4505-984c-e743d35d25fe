import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/lists/base_list.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/providers/ticket_providers.dart';
import 'package:opti_tickets/src/screens/tickets/view/widgets/ticket_card.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/report_fields.widget.dart';

class ReportsScreen extends HookConsumerWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedStartDateNotifier = useState<DateTime?>(
      DateTime(DateTime.now().year, DateTime.now().month, 1),
    );

    final selectedEndDateNotifier = useState<DateTime?>(DateTime.now());
    final statusNotifier = useState<TicketStatusModel?>(null);
    final sortNotifier = useState<String>(context.tr.descending);

    final params = (
      selectedStartDateNotifier.value.formatDateToString,
      selectedEndDateNotifier.value.formatDateToString
    );

    final getTicketReportsFuture =
        ref.watch(getTicketReportsFutureProvider(params));

    final tickets = getTicketReportsFuture.when(
      data: (tickets) => tickets,
      loading: () => <TicketModel>[],
      error: (error, stackTrace) => <TicketModel>[],
    );

    final filteredTickets = tickets
        .where(
          (ticket) =>
              statusNotifier.value == null ||
              statusNotifier.value?.id == 0 ||
              ticket.status?.value?.id == statusNotifier.value?.id,
        )
        .toList();

    // sort by date
    if (sortNotifier.value == context.tr.descending) {
      filteredTickets.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } else {
      filteredTickets.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    }

    final isLoading = getTicketReportsFuture.isLoading;

    final allTicketsStatusesWithoutDuplicates = tickets
        .map((ticket) => ticket.status?.value)
        .where((status) => status != null)
        .toSet()
        .toList();

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Text(
          "${context.tr.allTickets} (${tickets.length})",
          style: AppTextStyles.whiteTitle,
        ),
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppGaps.gap16,
          ReportFieldsWidget(
            selectedStartDateNotifier: selectedStartDateNotifier,
            selectedEndDateNotifier: selectedEndDateNotifier,
          ),
          AppGaps.gap8,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppGaps.gap8,
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.screenPadding + 6),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        flex: 2,
                        child: BaseDropDown(
                          onChanged: (value) {
                            statusNotifier.value = value;
                          },
                          isWhiteText: false,
                          data: allTicketsStatusesWithoutDuplicates
                            ..insert(
                                0,
                                const TicketStatusModel(
                                  id: 0,
                                  nameA: 'الكل',
                                  nameE: 'All',
                                )),
                          asString: (status) => context.isEnglish
                              ? (status as TicketStatusModel).nameE
                              : (status as TicketStatusModel).nameA,
                          label: context.tr.status,
                          selectedValue: statusNotifier.value,
                        ),
                      ),
                      AppGaps.gap8,
                      Flexible(
                          child: BaseDropDown(
                        onChanged: (value) {
                          sortNotifier.value = value;
                        },
                        isWhiteText: false,
                        data: [
                          context.tr.descending,
                          context.tr.ascending,
                        ],
                        label: context.tr.sortByDate,
                        selectedValue: sortNotifier.value,
                      )),
                    ],
                  ),
                ),
                AppGaps.gap4,
                Expanded(
                  child: Skeletonizer(
                    enabled: isLoading,
                    child: BaseList(
                        padding: const EdgeInsets.all(AppSpaces.screenPadding),
                        data: isLoading ? demoTickets : filteredTickets,
                        itemBuilder: (ticket, index) {
                          return TicketCard(
                            ticket: ticket,
                          );
                        }),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
