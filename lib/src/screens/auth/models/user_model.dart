import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

enum UserType { user, doctor, store }

class UserModel extends Equatable {
  final int? id;
  final String? name;
  final String? username;
  final String? mobile;
  final String? password;
  final String? deviceToken;

  const UserModel({
    this.id,
    this.name,
    this.username,
    this.mobile,
    this.password,
    this.deviceToken,
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'] ?? json['tperson'] ?? '',
      username: json['email'] ?? json['temail'] ?? '',
      mobile: json['mobile'] ?? json['tphone'] ?? '',
      deviceToken: json['device_token'] ?? '',
    );
  }

  //? Copy With

  // * To Json ================================
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email_phone': mobile,
      'device_token': deviceToken,
      'password': password,
      'password_confirmation': password,
    };
  }

  // * To Login Json ================================
  Map<String, dynamic> toLoginJson() {
    return {
      'username': username,
      'password': password,
      'type': 'mob',
      'token': deviceToken,
      'device_type': Platform.isAndroid ? 'Android' : 'iOS',
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        username,
        mobile,
        password,
        deviceToken,
      ];

  // copy with method
  UserModel copyWith({
    int? id,
    String? name,
    String? username,
    String? mobile,
    String? password,
    String? deviceToken,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      username: username ?? this.username,
      mobile: mobile ?? this.mobile,
      password: password ?? this.password,
      deviceToken: deviceToken ?? this.deviceToken,
    );
  }

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
