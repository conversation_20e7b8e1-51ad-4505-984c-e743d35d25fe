import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/tickets/models/program_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ProgramsSliderWidget extends HookConsumerWidget {
  final List<ProgramModel> programs;

  const ProgramsSliderWidget({
    super.key,
    required this.programs,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sliderIndex = useState(0);
    final homeFuture = ref.watch(getHomeFutureProvider);

    final isLoading = homeFuture.isLoading;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
          child: Text(
            context.tr.mySubscriptions,
            style: AppTextStyles.title,
          ),
        ),
        AppGaps.gap8,
        CarouselSlider(
          items: programs
              .map((program) => Container(
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.screenPadding),
                    padding: const EdgeInsets.all(AppSpaces.padding16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                        colors: [
                          ColorManager.lightGrey.withOpacity(0.8),
                          ColorManager.lightGrey.withOpacity(0.3),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Text(
                                  context.isEnglish
                                      ? program.programEn
                                      : program.programAr,
                                  style: AppTextStyles.subHeadLine,
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: AppSpaces.padding4),
                                  child: Text(
                                    ' - ${context.isEnglish ? program.subProgramEn : program.subProgramAr}',
                                    style: AppTextStyles.subTitle,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              width: 50.w,
                              height: 50.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(100),
                                image: DecorationImage(
                                  image: NetworkImage(program.logo),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          ],
                        ),
                        AppGaps.gap8,
                        Row(
                          children: [
                            CircleAvatar(
                              maxRadius: 15,
                              backgroundColor: isLoading
                                  ? Colors.transparent
                                  : ColorManager.errorColor,
                              child: const Icon(
                                Icons.settings,
                                color: ColorManager.white,
                                size: 18,
                              ),
                            ),
                            AppGaps.gap8,
                            Row(
                              children: [
                                Text(
                                  '${context.tr.remainingMaintenance}: ',
                                  style: AppTextStyles.subTitle,
                                ),
                                Text(
                                  '${program.remainingDays} ${context.tr.days}',
                                  style: AppTextStyles.whiteSubTitle.copyWith(
                                    color: program.isMoreThan30Day
                                        ? AppColors.successColor
                                        : ColorManager.errorColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ))
              .toList(),
          options: CarouselOptions(
            height: 100.h,
            onPageChanged: (index, reason) {
              sliderIndex.value = index;
            },
            viewportFraction: 1.0,
            initialPage: 0,
            enableInfiniteScroll: true,
            autoPlayInterval: const Duration(seconds: 3),
            autoPlayAnimationDuration: const Duration(milliseconds: 800),
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: false,
            scrollDirection: Axis.horizontal,
          ),
        ),
        AppGaps.gap8,
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: programs
              .asMap()
              .entries
              .map((e) => Container(
                    width: e.key == sliderIndex.value ? 30 : 15.w,
                    height: 8.0,
                    margin: const EdgeInsets.symmetric(
                        horizontal: AppSpaces.padding4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: e.key == sliderIndex.value && !isLoading
                          ? ColorManager.primaryColor
                          : ColorManager.lightGrey,
                    ),
                  ))
              .toList(),
        )
      ],
    );
  }
}
