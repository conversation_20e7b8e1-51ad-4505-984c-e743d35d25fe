import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/repositories/ticket_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class TicketController extends BaseVM {
  final TicketRepository ticketRepo;

  TicketController({
    required this.ticketRepo,
  });

  // * Get Ticket Details
  Future<TicketModel> getTicketDetails({
    required int? ticketId,
  }) async {
    return await baseFunction(
      () async {
        return await ticketRepo.getTicketDetails(ticketId: ticketId);
      },
    );
  }

  // * Get Ticket Reports by (Start Date, End Date)
  Future<List<TicketModel>> getTicketReports({
    required String? startDate,
    required String? endDate,
  }) async {
    return await baseFunction(
      () async {
        return await ticketRepo.getTicketReports(
          startDate: startDate,
          endDate: endDate,
        );
      },
    );
  }

  // * Add Reply
  Future<void> addReply({
    required ReplyModel reply,
    String? filePath,
  }) async {
    return await baseFunction(
      () async {
        return await ticketRepo.addReply(
          reply: reply,
          filePath: filePath,
        );
      },
    );
  }

  // * Add Ticket
  Future<void> addTicket({
    required TicketModel ticket,
    String? filePath,
  }) async {
    return await baseFunction(
      () async {
        return await ticketRepo.addTicket(
          ticket: ticket,
          filePath: filePath,
        );
      },
    );
  }
}
