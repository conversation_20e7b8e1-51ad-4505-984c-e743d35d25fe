import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/generated/assets.gen.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/change_language/change_language.widget.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/view/login/widgets/login_fields_container.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom > 0;
    return FormBuilder(
      key: formKey,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorManager.primaryColor,
        bottomNavigationBar: LoginFieldsContainer(
          formKey: formKey,
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isKeyboardOpened) AppGaps.gap12 else AppGaps.gap48,
            Padding(
              padding: const EdgeInsets.all(AppSpaces.screenPadding),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 70.r,
                    height: 70.r,
                    decoration: BoxDecoration(
                      color: ColorManager.white,
                      borderRadius: BorderRadius.circular(AppRadius.radius16),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppRadius.radius16),
                      child: Assets.images.logo.image(
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () => showDialog(
                          context: context,
                          builder: (_) => const ChangeLanguageWidget(),
                        ),
                        child: Container(
                          width: 50.r,
                          height: 50.r,
                          decoration: BoxDecoration(
                            color: ColorManager.white,
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius16),
                          ),
                          child: Text(context.isEnglish ? 'AR' : 'EN').center(),
                        ),
                      ),
                      AppGaps.gap12,
                      Container(
                          width: 50.r,
                          height: 50.r,
                          decoration: BoxDecoration(
                            color: ColorManager.white,
                            borderRadius:
                                BorderRadius.circular(AppRadius.radius16),
                          ),
                          child: const Icon(CupertinoIcons.phone)),
                    ],
                  ),
                ],
              ),
            ),
            if (!isKeyboardOpened) ...[
              AppGaps.gap24,
              Padding(
                padding: const EdgeInsets.all(AppSpaces.screenPadding),
                child: Text(
                  context.tr.welcomeBackLine,
                  style: AppTextStyles.whiteHeadLine.copyWith(
                    fontSize: 42,
                  ),
                ),
              ),
              AppGaps.gap24,
            ],
          ],
        ),
      ),
    );
  }
}
