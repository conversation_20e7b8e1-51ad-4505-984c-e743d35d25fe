import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/services/media/controller/media_controller.dart';
import 'package:opti_tickets/src/core/services/media/ui/media_picker.widget.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/providers/ticket_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/widgets/loading/loading_widget.dart';

class BottomReplySectionWidget extends HookConsumerWidget {
  final TicketModel ticket;
  final ScrollController scrollController;

  const BottomReplySectionWidget({
    super.key,
    required this.ticket,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);
    final ticketController = ref.watch(ticketControllerNotifierProvider);

    final replyController = useTextEditingController();

    final selectedFile = mediaPickerController.filePath;

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.padding12),
      child: Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (selectedFile.isNotEmpty) ...[
              const ViewLocalSelectedImage(),
              AppGaps.gap8,
            ],
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: replyController,
                    decoration: InputDecoration(
                      hintText: context.tr.reply,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(AppRadius.radius8),
                      ),
                    ),
                  ),
                ),
                ticketController.isLoading
                    ? const LoadingWidget(
                        height: 55,
                        width: 60,
                        isSmall: true,
                      )
                    : Row(
                        children: [
                          const AttachFileIcon(),
                          IconButton(
                            icon: const Icon(Icons.send),
                            onPressed: () async {
                              if (replyController.text.isEmpty) {
                                Fluttertoast.showToast(
                                  msg: context.tr.replyCannotBeEmpty,
                                  backgroundColor: ColorManager.errorColor,
                                );
                                return;
                              }

                              final replyUserName = GetStorageService.getData(
                                  key: LocalKeys.issuerName);

                              final replyModel = ReplyModel(
                                  message: replyController.text,
                                  ticketId: ticket.id,
                                  createdAt: DateTime.now(),
                                  attach: selectedFile,
                                  person: UserModel(
                                    name: replyUserName,
                                    username: GetStorageService.getData(
                                        key: LocalKeys.issuerEmail),
                                    mobile: GetStorageService.getData(
                                        key: LocalKeys.issuerPhone),
                                  ));

                              //? Send reply
                              await ticketController.addReply(
                                reply: replyModel,
                                filePath: selectedFile,
                              );

                              final ticketDetails =
                                  await ticketController.getTicketDetails(
                                ticketId: ticket.id,
                              );

                              ticket.status?.value =
                                  ticketDetails.status?.value ??
                                      ticket.status?.value ??
                                      const TicketStatusModel();

                              ticket.replies?.value =
                                  ticketDetails.replies?.value ?? [];

                              //? Clear reply field
                              replyController.clear();

                              //? Clear selected file
                              mediaPickerController.clearFiles();

                              //? Show success message
                              Fluttertoast.showToast(
                                msg: context.tr.replySentSuccessfully,
                                backgroundColor: ColorManager.successColor,
                              );

                              //? Scroll to bottom
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (scrollController.hasClients) {
                                  scrollController.animateTo(
                                    scrollController.position.maxScrollExtent,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeOut,
                                  );
                                }
                              });
                            },
                          ),
                        ],
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
