import 'package:equatable/equatable.dart';

class ProgramModel extends Equatable {
  final String programEn;
  final String programAr;
  final String subProgramEn;
  final String subProgramAr;
  final String logo;
  final num remainingDays;

  const ProgramModel({
    this.programEn = '',
    this.programAr = '',
    this.subProgramEn = '',
    this.subProgramAr = '',
    this.logo = '',
    this.remainingDays = 0,
  });

  bool get isMoreThan30Day => remainingDays > 30;

  static List<ProgramModel> programs = [
    const ProgramModel(
      logo:
          'https://img.freepik.com/free-vector/flat-horizontal-sale-banner-template-national-pet-day-with-animals_23-**********.jpg',
      programEn: 'ERP System',
      subProgramEn: ' - Clearance',
      remainingDays: 20,
    ),
    const ProgramModel(
      programEn: 'ERP System',
      logo:
          'https://img.freepik.com/premium-vector/weekend-sale-banner-template-promotion_8499-1113.jpg',
      subProgramEn: ' - Accounting',
      remainingDays: 45,
    ),
  ];

  // * From Json
  factory ProgramModel.fromJson(Map<String, dynamic> json) {
    return ProgramModel(
      programEn: json['serviceE'] ?? '',
      programAr: json['serviceA'] ?? '',
      subProgramEn: json['subserviceE'] ?? '',
      subProgramAr: json['subserviceA'] ?? '',
      logo: json['serviceLogo'] ?? '',
      remainingDays: json['mc_days'] ?? 0,
    );
  }

  factory ProgramModel.empty() => const ProgramModel();

  @override
  List<Object?> get props => [
        programEn,
        programAr,
        subProgramEn,
        subProgramAr,
        remainingDays,
        logo,
      ];
}
