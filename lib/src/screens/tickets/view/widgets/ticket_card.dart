import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/extensions/string_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/core/utils/generate_colors.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/providers/ticket_providers.dart';
import 'package:opti_tickets/src/screens/tickets/view/ticket_details/ticket_details.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class TicketCard extends ConsumerWidget {
  final TicketModel ticket;
  final bool fromDetails;

  const TicketCard({
    super.key,
    required this.ticket,
    this.fromDetails = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeFuture = ref.watch(homeControllerProvider);
    final ticketController = ref.watch(ticketControllerProvider);

    final isLoading = homeFuture.isLoading || ticketController.isLoading;

    Color randomColor =
        getRandomColorByNumber(ticket.issuer?.name.hashCode ?? 0);

    return GestureDetector(
      onTap: () {
        if (fromDetails) return;
        TicketDetailsScreen(
          ticket: ticket,
        ).navigate;
      },
      child: Container(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        decoration: BoxDecoration(
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.radius12),
          boxShadow: [
            BoxShadow(
              color: ColorManager.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ticket.description,
                        style: AppTextStyles.subTitle,
                        maxLines: fromDetails ? null : 3,
                        overflow: fromDetails ? null : TextOverflow.ellipsis,
                      ),
                      AppGaps.gap8,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            ticket.code,
                            style:
                                AppTextStyles.subTitle.copyWith(fontSize: 14),
                          ),
                          AppGaps.gap8,
                          Expanded(
                            child: Align(
                              alignment: context.isEnglish
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft,
                              child: Text(
                                  context.isEnglish
                                      ? (ticket.status?.value?.nameE ?? '')
                                      : (ticket.status?.value?.nameA ?? ''),
                                  style: AppTextStyles.subTitle.copyWith(
                                    fontSize: 14,
                                    color: ticket
                                        .status?.value?.color.convertHexToColor,
                                  )),
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            AppGaps.gap8,
            Divider(
              color: ColorManager.darkGrey.withOpacity(.4),
            ),
            AppGaps.gap8,
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: isLoading ? ColorManager.grey : randomColor,
                  child: Text(
                    isLoading ? '' : ticket.issuer?.name?.substring(0, 1) ?? '',
                    style: AppTextStyles.whiteTitle.copyWith(
                      fontSize: 20,
                      color: getTextColor(randomColor),
                    ),
                  ),
                ),
                AppGaps.gap8,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ticket.issuer?.name ?? '',
                      style: AppTextStyles.labelLarge,
                    ),
                    Text(
                      ticket.createdAt,
                      style: AppTextStyles.greyHint,
                    ),
                  ],
                ),
              ],
            ),
            // AppGaps.gap16,
            // ExpansionTile(
            //   title: Text(
            //     'Replies (2)',
            //     style: AppTextStyles.title,
            //   ),
            //   children: ticket.replies
            //       .map((reply) => Padding(
            //             padding: const EdgeInsets.symmetric(
            //                 vertical: AppSpaces.padding4),
            //             child: Text(
            //               reply.reply,
            //               style: AppTextStyles.whiteLabelMedium,
            //             ),
            //           ))
            //       .toList(),
            // ),
          ],
        ),
      ),
    );
  }
}

// class TicketCard extends StatelessWidget {
//   final TicketModel ticket;
//
//   const TicketCard({
//     super.key,
//     required this.ticket,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       padding: const EdgeInsets.all(AppSpaces.padding16),
//       decoration: BoxDecoration(
//         color: ColorManager.primaryColor,
//         borderRadius: BorderRadius.circular(AppRadius.radius12),
//       ),
//       child: Row(
//         children: [
//           Icon(
//             FontAwesomeIcons.ticket,
//             color: ColorManager.white,
//             size: 40.sp,
//           ),
//           AppGaps.gap16,
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   "Opened Tickets",
//                   style: AppTextStyles.whiteTitle,
//                 ),
//                 Text(
//                   "You have 3 opened tickets",
//                   style: AppTextStyles.whiteTitle,
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
