import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/services/media/controller/media_controller.dart';
import 'package:opti_tickets/src/core/services/media/ui/media_picker.widget.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/providers/ticket_providers.dart';
import 'package:opti_tickets/src/screens/tickets/view/ticket_details/widgets/bottom_reply_section.widget.dart';
import 'package:opti_tickets/src/screens/tickets/view/widgets/ticket_card.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/replies_section.widget.dart';

class TicketDetailsScreen extends HookConsumerWidget {
  final TicketModel ticket;

  const TicketDetailsScreen({
    super.key,
    required this.ticket,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);
    final scrollController = useScrollController();

    final ticketDetailsFuture =
        ref.watch(getTicketDetailsFutureProvider(ticket.id));

    final ticketDetails = ticketDetailsFuture.when(
      data: (ticketDetails) => ticketDetails,
      loading: () => const TicketModel(),
      error: (error, stackTrace) => const TicketModel(),
    );

    useEffect(() {
      ref.refresh(getTicketDetailsFutureProvider(ticket.id));

      return () {};
    }, []);

    final isKeyboardOpened = MediaQuery.of(context).viewInsets.bottom > 0;

    return WillPopScope(
      onWillPop: () async {
        ref.refresh(getHomeFutureProvider);
        ref.refresh(getTicketDetailsFutureProvider(ticket.id));
        return true;
      },
      child: Scaffold(
        bottomNavigationBar:
            ticketDetailsFuture.isLoading || ticketDetails.closeReplies
                ? null
                : BottomReplySectionWidget(
                    ticket: ticketDetails,
                    scrollController: scrollController,
                  ),
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back_ios_new,
              color: Colors.white,
            ),
            onPressed: () {
              navService.back();

              ref.refresh(getHomeFutureProvider);
              ref.refresh(getTicketDetailsFutureProvider(ticket.id));
            },
          ),
          centerTitle: true,
          title: Text(
            ticket.code,
            style: AppTextStyles.whiteTitle,
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(AppSpaces.screenPadding),
          child: ticketDetailsFuture.isLoading
              ? const Center(
                  child: LoadingWidget(
                    height: 300,
                    width: 300,
                  ),
                )
              : HookBuilder(builder: (context) {
                  useEffect(() {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (scrollController.hasClients) {
                        scrollController
                            .jumpTo(scrollController.position.maxScrollExtent);
                      }
                    });
                    return () {};
                  }, []);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (!isKeyboardOpened) ...[
                        TicketCard(
                          ticket: ticketDetails,
                          fromDetails: true,
                        ),

                        // * Attachment
                        if (ticketDetails.image.isNotEmpty) ...[
                          AppGaps.gap16,
                          ViewNetworkFile(
                            fileUrl: ticketDetails.image,
                          ),
                        ],

                        AppGaps.gap16,
                      ],
                      Text(
                        context.tr.replies,
                        style: AppTextStyles.title,
                      ),

                      AppGaps.gap12,

                      // * Replies
                      Expanded(
                        child: SingleChildScrollView(
                          controller: scrollController,
                          child: RepliesSection(
                            ticketDetails: ticketDetails,
                          ),
                        ),
                      ),
                    ],
                  );
                }),
        ),
      ),
    );
  }
}
