import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/consts/network/api_strings.dart';
import 'package:opti_tickets/src/core/services/media/controller/media_controller.dart';
import 'package:opti_tickets/src/core/services/media/ui/media_picker.widget.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/widgets/fields/text_field.dart';
import 'package:opti_tickets/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_providers.dart';
import 'package:opti_tickets/src/screens/tickets/models/ticket_model.dart';
import 'package:opti_tickets/src/screens/tickets/providers/ticket_providers.dart';
import 'package:opti_tickets/src/screens/tickets/view/add_ticket_sheet/widgets/ticket_type.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class AddTicketSheetWidget extends HookConsumerWidget {
  const AddTicketSheetWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    final issuerName = GetStorageService.getData(key: LocalKeys.issuerName);
    final issuerEmail = GetStorageService.getData(key: LocalKeys.issuerEmail);
    final issuerPhone = GetStorageService.getData(key: LocalKeys.issuerPhone);

    final isIssue = useState<bool>(true);

    void saveToLocalStorage({
      required String? issuerName,
      required String? issuerEmail,
      required String? issuerPhone,
    }) {
      GetStorageService.setData(key: LocalKeys.issuerName, value: issuerName);
      GetStorageService.setData(key: LocalKeys.issuerEmail, value: issuerEmail);
      GetStorageService.setData(key: LocalKeys.issuerPhone, value: issuerPhone);
    }

    final ticketController = ref.watch(ticketControllerNotifierProvider);
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);
    final selectedFile = mediaPickerController.filePath;

    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Material(
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        child: FormBuilder(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppGaps.gap12,
                Text(
                  context.tr.addNewTicket,
                  style: AppTextStyles.title,
                ),
                AppGaps.gap12,
                TicketTypeWidget(
                  isIssue: isIssue,
                ),
                AppGaps.gap24,
                BaseTextField(
                  name: FieldsConsts.issueName,
                  label: context.tr.issuerName,
                  initialValue: issuerName,
                  icon: const Icon(CupertinoIcons.person),
                ),
                AppGaps.gap12,
                BaseTextField(
                  initialValue: issuerEmail,
                  name: FieldsConsts.issueEmail,
                  label: context.tr.issuerEmail,
                  icon: const Icon(CupertinoIcons.mail),
                ),
                AppGaps.gap12,
                BaseTextField(
                  initialValue: issuerPhone,
                  name: FieldsConsts.issuePhone,
                  label: context.tr.issuerPhone,
                  icon: const Icon(CupertinoIcons.phone),
                ),
                AppGaps.gap12,
                BaseTextField(
                  name: FieldsConsts.description,
                  label: context.tr.description,
                  maxLines: 3,
                  icon: const Icon(FontAwesomeIcons.comment),
                ),
                AppGaps.gap24,
                if (selectedFile.isNotEmpty) ...[
                  const ViewLocalSelectedImage(),
                  AppGaps.gap24,
                ],
                Row(
                  children: [
                    Expanded(
                      child: Button(
                        isLoading: ticketController.isLoading,
                        loadingWidget: const LoadingWidget(),
                        label: context.tr.submit,
                        onPressed: () async {
                          if (!formKey.currentState!.saveAndValidate()) return;

                          final data = formKey.currentState?.instantValue ?? {};

                          saveToLocalStorage(
                            issuerName: data[FieldsConsts.issueName],
                            issuerEmail: data[FieldsConsts.issueEmail],
                            issuerPhone: data[FieldsConsts.issuePhone],
                          );

                          final ticketModel = TicketModel(
                            description: data[FieldsConsts.description],
                            ticketType: isIssue.value ? 'I' : 'R',
                            issuer: UserModel(
                              name: data[FieldsConsts.issueName],
                              username: data[FieldsConsts.issueEmail],
                              mobile: data[FieldsConsts.issuePhone],
                            ),
                          );

                          await ticketController.addTicket(
                            ticket: ticketModel,
                            filePath: selectedFile,
                          );

                          mediaPickerController.clearFiles();

                          ref.refresh(getHomeFutureProvider);

                          navService.back();
                        },
                      ),
                    ),
                    const AttachFileIcon(),
                  ],
                )
              ],
            ).paddingAll(AppSpaces.screenPadding),
          ),
        ),
      ),
    );
  }
}
