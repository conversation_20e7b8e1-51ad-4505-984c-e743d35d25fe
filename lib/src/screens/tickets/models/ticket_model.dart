import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';

class TicketModel extends Equatable {
  final int? id;
  final String code;
  final String createdAt;
  final String description;
  final String image;
  final ValueNotifier<List<ReplyModel>>? replies;
  final ValueNotifier<TicketStatusModel>? status;
  final UserModel? issuer;
  final bool closeReplies;
  final String ticketType;

  const TicketModel({
    this.id,
    this.code = '',
    this.description = '',
    this.image = '',
    this.replies,
    this.createdAt = '',
    this.ticketType = 'I',
    this.status,
    this.issuer,
    this.closeReplies = false,
  });

  // * From Json
  factory TicketModel.fromJson(Map<String, dynamic> json) {
    return TicketModel(
      id: json['tid'],
      code: json['tcode'] ?? '',
      description: json['tmsg'] ?? '',
      issuer: json['tby'] != null ? UserModel.fromJson(json['tby']) : null,
      status: json['tstatus'] != null
          ? ValueNotifier(TicketStatusModel.fromJson(json['tstatus']))
          : ValueNotifier(const TicketStatusModel()),
      image: json['tattach'] ?? '',
      createdAt: json['tcreatedat'] ?? '',
      replies: json['tresponses'] != null
          ? ValueNotifier((json['tresponses'] as List)
              .map((e) => ReplyModel.fromJson(e))
              .toList())
          : ValueNotifier([]),
      closeReplies: json['taction'] == false,
      ticketType: json['ticketType'] ?? 'I',
    );
  }

  // * To Json
  Map<String, dynamic> toJson({String? deviceToken}) {
    return {
      'ticketType': ticketType, // R or I (Request - Issue)
      'ticketSource': 'mobile',
      'ticketMsg': description,
      'tl_personName': issuer?.name,
      'tl_mobile': issuer?.mobile,
      'tl_email': issuer?.username,
      'device_token': deviceToken,
    };
  }

  factory TicketModel.empty() => const TicketModel();

  @override
  List<Object?> get props => [
        id,
      ];
}

class ReplyModel extends Equatable {
  final int? ticketId;
  final String type;
  final String message;
  final String attach;
  final UserModel? person;
  final DateTime? createdAt;

  const ReplyModel({
    this.ticketId,
    this.message = '',
    this.type = '',
    this.attach = '',
    this.person,
    this.createdAt,
  });

  // * From Json
  factory ReplyModel.fromJson(Map<String, dynamic> json) {
    return ReplyModel(
      ticketId: json['id'],
      message: json['msg'] ?? '',
      type: json['type'] ?? '',
      attach: json['attach'] ?? '',
      person:
          json['person'] != null ? UserModel.fromJson(json['person']) : null,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  // * To Json
  Map<String, dynamic> toJson({
    String? deviceToken,
  }) {
    return {
      'tid': ticketId,
      'th_msg': message,
      'th_person': person?.name ?? 'Guest',
      'th_mobile': person?.mobile ?? 'Guest Mobile',
      'th_email': person?.username ?? '<EMAIL>',
      'device_token': deviceToken,
    };
  }

  factory ReplyModel.empty() => const ReplyModel();

  @override
  List<Object?> get props => [
        ticketId,
        message,
      ];
}

class TicketStatusModel extends Equatable {
  final int? id;
  final String nameE;
  final String nameA;
  final String color;

  const TicketStatusModel({
    this.id,
    this.nameE = '',
    this.nameA = '',
    this.color = '',
  });

  // * From Json
  factory TicketStatusModel.fromJson(Map<String, dynamic> json) {
    return TicketStatusModel(
      id: json['tsID'],
      nameE: json['tsnameE'] ?? '',
      nameA: json['tsnameA'] ?? '',
      color: json['tsRGB'] ?? '',
    );
  }

  factory TicketStatusModel.empty() => const TicketStatusModel();

  @override
  List<Object?> get props => [
        id,
        nameE,
        nameA,
        color,
      ];
}

final demoTickets = [
  TicketModel(
    id: 1,
    code: 'TICKET-001',
    description: 'Description of issue 1',
    image: 'https://example.com/image1.png',
    createdAt: '2023-10-01T12:00:00Z',
    replies: ValueNotifier([
      ReplyModel(
        message: 'Reply 1',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 1'),
      ),
      ReplyModel(
        message: 'Reply 2',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 2'),
      ),
      ReplyModel(
        message: 'Reply 3',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 3'),
      ),
    ]),
    status: ValueNotifier(const TicketStatusModel(
      id: 1,
      nameE: 'Open',
      color: 'ff9f43',
    )),
    issuer: const UserModel(name: 'Issuer 1'),
  ),
  TicketModel(
    id: 2,
    code: 'TICKET-002',
    description: 'Description of issue 2',
    image: 'https://example.com/image2.png',
    createdAt: '2023-10-02T12:00:00Z',
    replies: ValueNotifier([
      ReplyModel(
        message: 'Reply 1',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 1'),
      ),
      ReplyModel(
        message: 'Reply 2',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 2'),
      ),
      ReplyModel(
        message: 'Reply 3',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 3'),
      ),
    ]),
    status: ValueNotifier(const TicketStatusModel(
      id: 2,
      nameE: 'Closed',
      color: 'ff9f43',
    )),
    issuer: const UserModel(name: 'Issuer 2'),
  ),
  TicketModel(
    id: 3,
    code: 'TICKET-003',
    description: 'Description of issue 3',
    image: 'https://example.com/image3.png',
    createdAt: '2023-10-03T12:00:00Z',
    replies: ValueNotifier([
      ReplyModel(
        message: 'Reply 1',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 1'),
      ),
      ReplyModel(
        message: 'Reply 2',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 2'),
      ),
      ReplyModel(
        message: 'Reply 3',
        createdAt: DateTime.now(),
        person: const UserModel(name: 'User 3'),
      ),
    ]),
    status: ValueNotifier(
      const TicketStatusModel(
        id: 3,
        nameE: 'In Progress',
        color: 'ff9f43',
      ),
    ),
    issuer: const UserModel(name: 'Issuer 3'),
  ),
];
